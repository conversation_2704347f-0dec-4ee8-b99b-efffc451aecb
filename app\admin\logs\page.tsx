"use client";

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, Shield, Info, AlertCircle, Trash2, RefreshCw, TrendingUp, Activity, Eye, Clock, Globe, Users } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, AreaChart, Area } from 'recharts';

interface LogEntry {
  timestamp: string;
  level: string;
  category: string;
  message: string;
  ip?: string;
  userAgent?: string;
  userId?: string;
  details?: Record<string, unknown>;
}

interface LogStats {
  total: number;
  byCategory: Record<string, number>;
  byHour: Record<string, number>;
}

interface LogResponse {
  logs: LogEntry[];
  statistics: LogStats | null;
  level: string;
  total: number;
}

// Color schemes for charts
const COLORS = {
  primary: '#3b82f6',
  secondary: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#6366f1',
  success: '#22c55e'
};

const CHART_COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#6366f1', '#22c55e', '#8b5cf6', '#f97316'];

export default function LogsPage() {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [statistics, setStatistics] = useState<LogStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [level, setLevel] = useState('SECURITY');
  const [limit, setLimit] = useState('50');
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  const fetchLogs = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/admin/logs?level=${level}&limit=${limit}&stats=true`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: LogResponse = await response.json();
      setLogs(data.logs);
      setStatistics(data.statistics);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch logs');
      console.error('Error fetching logs:', err);
    } finally {
      setLoading(false);
    }
  }, [level, limit]);

  const clearOldLogs = async () => {
    if (!confirm('Are you sure you want to clear logs older than 30 days?')) {
      return;
    }

    try {
      const response = await fetch('/api/admin/logs?days=30', {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to clear logs');
      }

      alert('Old logs cleared successfully');
      fetchLogs(); // Refresh the logs
    } catch (err) {
      alert('Failed to clear logs: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  const toggleAutoRefresh = () => {
    if (autoRefresh) {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
      setAutoRefresh(false);
    } else {
      const interval = setInterval(fetchLogs, 30000); // Refresh every 30 seconds
      setRefreshInterval(interval);
      setAutoRefresh(true);
    }
  };

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [refreshInterval]);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  // Prepare chart data
  const prepareHourlyData = () => {
    if (!statistics?.byHour) return [];

    const hours = Object.entries(statistics.byHour)
      .map(([hour, count]) => ({
        hour: new Date(hour + ':00:00').toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' }),
        events: count,
        timestamp: hour
      }))
      .sort((a, b) => a.timestamp.localeCompare(b.timestamp))
      .slice(-12); // Last 12 hours

    return hours;
  };

  const prepareCategoryData = () => {
    if (!statistics?.byCategory) return [];

    return Object.entries(statistics.byCategory)
      .map(([category, count], index) => ({
        name: category,
        value: count,
        color: CHART_COLORS[index % CHART_COLORS.length]
      }))
      .sort((a, b) => b.value - a.value);
  };

  const prepareRecentActivity = () => {
    const last24Hours = Array.from({ length: 24 }, (_, i) => {
      const hour = new Date();
      hour.setHours(hour.getHours() - (23 - i), 0, 0, 0);
      return {
        hour: hour.toLocaleTimeString('it-IT', { hour: '2-digit' }),
        events: 0,
        timestamp: hour.toISOString().substring(0, 13)
      };
    });

    if (statistics?.byHour) {
      Object.entries(statistics.byHour).forEach(([hourStr, count]) => {
        const hourData = last24Hours.find(h => h.timestamp === hourStr);
        if (hourData) {
          hourData.events = count;
        }
      });
    }

    return last24Hours;
  };

  const getThreatLevel = () => {
    if (!statistics) return { level: 'LOW', color: 'text-green-600', bgColor: 'bg-green-100' };

    const total = statistics.total;
    if (total > 100) return { level: 'HIGH', color: 'text-red-600', bgColor: 'bg-red-100' };
    if (total > 50) return { level: 'MEDIUM', color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
    return { level: 'LOW', color: 'text-green-600', bgColor: 'bg-green-100' };
  };

  const getTopIPs = () => {
    const ipCounts: Record<string, number> = {};

    logs.forEach(log => {
      if (log.ip && log.ip !== 'unknown') {
        ipCounts[log.ip] = (ipCounts[log.ip] || 0) + 1;
      }
    });

    return Object.entries(ipCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([ip, count]) => ({ ip, count }));
  };

  const getLevelIcon = (logLevel: string) => {
    switch (logLevel) {
      case 'SECURITY':
        return <Shield className="h-4 w-4" />;
      case 'ERROR':
        return <AlertCircle className="h-4 w-4" />;
      case 'WARN':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getLevelColor = (logLevel: string) => {
    switch (logLevel) {
      case 'SECURITY':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'ERROR':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'WARN':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('it-IT');
  };

  const hourlyData = prepareHourlyData();
  const categoryData = prepareCategoryData();
  const activityData = prepareRecentActivity();
  const threatLevel = getThreatLevel();
  const topIPs = getTopIPs();

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
          <span className="ml-2 text-lg">Caricamento dashboard sicurezza...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Dashboard Sicurezza
          </h1>
          <p className="text-muted-foreground">
            Monitora gli eventi di sicurezza e le attività del sistema in tempo reale
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={toggleAutoRefresh}
            variant={autoRefresh ? "default" : "outline"}
            className={autoRefresh ? "bg-green-600 hover:bg-green-700" : ""}
          >
            <Activity className="h-4 w-4 mr-2" />
            {autoRefresh ? 'Auto-Refresh ON' : 'Auto-Refresh OFF'}
          </Button>
          <Button onClick={fetchLogs} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Aggiorna
          </Button>
          <Button onClick={clearOldLogs} variant="destructive">
            <Trash2 className="h-4 w-4 mr-2" />
            Pulisci Log
          </Button>
        </div>
      </div>

      {/* Threat Level Alert */}
      <Card className={`border-l-4 ${threatLevel.level === 'HIGH' ? 'border-l-red-500 bg-red-50' :
                                     threatLevel.level === 'MEDIUM' ? 'border-l-yellow-500 bg-yellow-50' :
                                     'border-l-green-500 bg-green-50'}`}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-full ${threatLevel.bgColor}`}>
                <Shield className={`h-6 w-6 ${threatLevel.color}`} />
              </div>
              <div>
                <h3 className="font-semibold">Livello di Minaccia: {threatLevel.level}</h3>
                <p className="text-sm text-muted-foreground">
                  {threatLevel.level === 'HIGH' && 'Attenzione: Attività sospetta elevata rilevata'}
                  {threatLevel.level === 'MEDIUM' && 'Monitoraggio: Attività moderata in corso'}
                  {threatLevel.level === 'LOW' && 'Tutto normale: Nessuna minaccia rilevata'}
                </p>
              </div>
            </div>
            <Badge variant={threatLevel.level === 'HIGH' ? 'destructive' :
                           threatLevel.level === 'MEDIUM' ? 'secondary' : 'default'}>
              {statistics?.total || 0} eventi (24h)
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Activity Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Attività nelle Ultime 24 Ore
            </CardTitle>
            <CardDescription>
              Distribuzione degli eventi di sicurezza per ora
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={activityData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="hour"
                  tick={{ fontSize: 12 }}
                  interval="preserveStartEnd"
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip
                  labelFormatter={(label) => `Ora: ${label}`}
                  formatter={(value) => [value, 'Eventi']}
                />
                <Area
                  type="monotone"
                  dataKey="events"
                  stroke={COLORS.primary}
                  fill={COLORS.primary}
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Category Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Distribuzione per Categoria
            </CardTitle>
            <CardDescription>
              Tipi di eventi di sicurezza rilevati
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={categoryData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {categoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [value, 'Eventi']} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity and Top IPs */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Hourly Activity */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Attività Recente (Ultime 12 Ore)
            </CardTitle>
            <CardDescription>
              Trend degli eventi di sicurezza
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <LineChart data={hourlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" tick={{ fontSize: 12 }} />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip
                  labelFormatter={(label) => `Ora: ${label}`}
                  formatter={(value) => [value, 'Eventi']}
                />
                <Line
                  type="monotone"
                  dataKey="events"
                  stroke={COLORS.primary}
                  strokeWidth={3}
                  dot={{ fill: COLORS.primary, strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Top IPs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Top IP Addresses
            </CardTitle>
            <CardDescription>
              Indirizzi IP più attivi
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topIPs.length > 0 ? (
                topIPs.map((item, index) => (
                  <div key={item.ip} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        index === 0 ? 'bg-red-500' :
                        index === 1 ? 'bg-orange-500' :
                        index === 2 ? 'bg-yellow-500' : 'bg-gray-400'
                      }`} />
                      <span className="font-mono text-sm">{item.ip}</span>
                    </div>
                    <Badge variant="secondary">{item.count} eventi</Badge>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Globe className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Nessun IP rilevato</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Filtri e Controlli</CardTitle>
          <CardDescription>
            Personalizza la visualizzazione dei log
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-wrap gap-4">
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Livello Log</label>
            <Select value={level} onValueChange={setLevel}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="SECURITY">🔒 Sicurezza</SelectItem>
                <SelectItem value="ERROR">❌ Errori</SelectItem>
                <SelectItem value="WARN">⚠️ Avvisi</SelectItem>
                <SelectItem value="INFO">ℹ️ Info</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Numero Log</label>
            <Select value={limit} onValueChange={setLimit}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="200">200</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Aggiornamento</label>
            <div className="flex items-center gap-2">
              <Badge variant={autoRefresh ? "default" : "secondary"}>
                {autoRefresh ? "🟢 Automatico" : "⏸️ Manuale"}
              </Badge>
              {autoRefresh && (
                <span className="text-xs text-muted-foreground">ogni 30s</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Eventi Totali (24h)</p>
                  <p className="text-3xl font-bold text-blue-900">{statistics.total}</p>
                  <p className="text-xs text-blue-600 mt-1">
                    <TrendingUp className="inline h-3 w-3 mr-1" />
                    Monitoraggio attivo
                  </p>
                </div>
                <Activity className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Log Visualizzati</p>
                  <p className="text-3xl font-bold text-green-900">{logs.length}</p>
                  <p className="text-xs text-green-600 mt-1">
                    <Eye className="inline h-3 w-3 mr-1" />
                    su {limit} richiesti
                  </p>
                </div>
                <Eye className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">Categorie Attive</p>
                  <p className="text-3xl font-bold text-purple-900">
                    {Object.keys(statistics.byCategory).length}
                  </p>
                  <p className="text-xs text-purple-600 mt-1">
                    <Globe className="inline h-3 w-3 mr-1" />
                    Tipi di eventi
                  </p>
                </div>
                <Globe className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-600">IP Unici</p>
                  <p className="text-3xl font-bold text-orange-900">{topIPs.length}</p>
                  <p className="text-xs text-orange-600 mt-1">
                    <Users className="inline h-3 w-3 mr-1" />
                    Sorgenti attive
                  </p>
                </div>
                <Users className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <span>Errore: {error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Logs Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Log Eventi Dettagliati
          </CardTitle>
          <CardDescription>
            Eventi recenti di livello {level.toLowerCase()} con analisi dettagliata
          </CardDescription>
        </CardHeader>
        <CardContent>
          {logs.length === 0 ? (
            <div className="text-center py-12">
              <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Activity className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Nessun log trovato</h3>
              <p className="text-muted-foreground">
                Non ci sono eventi per i criteri selezionati. Prova a cambiare i filtri o il livello di log.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {logs.map((log, index) => (
                <div key={index} className={`border rounded-xl p-5 space-y-3 transition-all hover:shadow-md ${
                  log.level === 'SECURITY' ? 'border-red-200 bg-red-50/50' :
                  log.level === 'ERROR' ? 'border-orange-200 bg-orange-50/50' :
                  log.level === 'WARN' ? 'border-yellow-200 bg-yellow-50/50' :
                  'border-blue-200 bg-blue-50/50'
                }`}>
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-full ${
                        log.level === 'SECURITY' ? 'bg-red-100' :
                        log.level === 'ERROR' ? 'bg-orange-100' :
                        log.level === 'WARN' ? 'bg-yellow-100' :
                        'bg-blue-100'
                      }`}>
                        {getLevelIcon(log.level)}
                      </div>
                      <div>
                        <Badge className={getLevelColor(log.level)}>
                          {log.level}
                        </Badge>
                        <Badge variant="outline" className="ml-2">{log.category}</Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {formatTimestamp(log.timestamp)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        #{index + 1}
                      </div>
                    </div>
                  </div>

                  {/* Message */}
                  <div className="bg-white rounded-lg p-3 border">
                    <div className="text-sm font-medium text-gray-900 mb-1">Messaggio:</div>
                    <div className="text-sm text-gray-700">{log.message}</div>
                  </div>

                  {/* Metadata */}
                  {(log.ip || log.userAgent || log.userId) && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      {log.ip && (
                        <div className="bg-white rounded-lg p-3 border">
                          <div className="text-xs font-medium text-gray-500 mb-1">IP ADDRESS</div>
                          <div className="font-mono text-sm">{log.ip}</div>
                        </div>
                      )}
                      {log.userId && (
                        <div className="bg-white rounded-lg p-3 border">
                          <div className="text-xs font-medium text-gray-500 mb-1">USER ID</div>
                          <div className="font-mono text-sm">{log.userId}</div>
                        </div>
                      )}
                      {log.userAgent && (
                        <div className="bg-white rounded-lg p-3 border md:col-span-2">
                          <div className="text-xs font-medium text-gray-500 mb-1">USER AGENT</div>
                          <div className="text-sm truncate" title={log.userAgent}>
                            {log.userAgent}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Technical Details */}
                  {log.details && (
                    <details className="group">
                      <summary className="cursor-pointer text-sm font-medium text-gray-600 hover:text-gray-900 flex items-center gap-2">
                        <span>Dettagli Tecnici</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">JSON</span>
                      </summary>
                      <div className="mt-3 bg-gray-900 rounded-lg p-4 overflow-x-auto">
                        <pre className="text-xs text-green-400 font-mono">
                          {JSON.stringify(log.details, null, 2)}
                        </pre>
                      </div>
                    </details>
                  )}
                </div>
              ))}

              {/* Load More Indicator */}
              {logs.length >= parseInt(limit) && (
                <div className="text-center py-4 border-t">
                  <p className="text-sm text-muted-foreground">
                    Visualizzati {logs.length} di {limit} log richiesti.
                    <br />
                    Aumenta il limite per vedere più eventi.
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
