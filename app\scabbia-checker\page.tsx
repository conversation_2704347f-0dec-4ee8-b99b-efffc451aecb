"use client";
import { useState, useEffect } from "react";
import Link from "next/link";
import { useOpenAI } from "@/lib/api-utils";
import { toast } from "react-hot-toast";
import { scabiesOraclePrompt } from "@/lib/scabiesPrompt";
import { motion, AnimatePresence } from "framer-motion";
import { Progress } from "@/components/ui/progress";
import { QuestionCard } from "@/components/ui/animated-card";
import { ArrowLeft, Sparkles, Brain, Stethoscope } from "lucide-react";
import Navbar from "../components/Navbar";
import Footer from "../components/Footer";

const questions = [
  {
    text: "Hai prurito intenso, soprattutto di notte?",
    icon: "🌙",
    description: "Il prurito notturno è uno dei sintomi più caratteristici della scabbia"
  },
  {
    text: "Hai notato piccoli rilievi o vescicole sulla pelle?",
    icon: "🔍",
    description: "Piccole eruzioni cutanee possono indicare la presenza di acari"
  },
  {
    text: "Qualcuno nella tua famiglia ha sintomi simili?",
    icon: "👨‍👩‍👧‍👦",
    description: "La scabbia è altamente contagiosa tra i contatti stretti"
  },
  {
    text: "Hai notato linee sottili sulla pelle che sembrano cunicoli?",
    icon: "〰️",
    description: "I cunicoli sono tracce lasciate dagli acari sotto la pelle"
  },
  {
    text: "Le zone colpite includono mani, polsi, ascelle o genitali?",
    icon: "👐",
    description: "Queste sono le aree più comunemente colpite dalla scabbia"
  },
  {
    text: "Hai dormito di recente a stretto contatto con altre persone?",
    icon: "🛏️",
    description: "Il contatto prolungato pelle-a-pelle facilita la trasmissione"
  },
  {
    text: "Hai la pelle irritata tra le dita?",
    icon: "✋",
    description: "Gli spazi tra le dita sono zone tipiche di infestazione"
  },
  {
    text: "I sintomi peggiorano dopo bagni caldi?",
    icon: "🚿",
    description: "Il calore può intensificare il prurito causato dalla scabbia"
  },
  {
    text: "Hai notato crosticine o noduli?",
    icon: "🔴",
    description: "Lesioni secondarie possono svilupparsi a causa del grattamento"
  },
  {
    text: "Ti è stata diagnosticata la scabbia in passato?",
    icon: "📋",
    description: "Una storia precedente aumenta la probabilità di reinfezione"
  },
];

export default function ScabbiaChecker() {
  const [index, setIndex] = useState(-1); // Start with -1 to show intro
  const [answers, setAnswers] = useState<boolean[]>([]);
  const [loading, setLoading] = useState(false);
  const [showPay, setShowPay] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [showIntro, setShowIntro] = useState(true);
  const { callAPI } = useOpenAI();

  // Show error toast when API error occurs
  useEffect(() => {
    if (apiError) {
      toast.error(apiError, {
        position: 'top-center',
        duration: 5000,
      });
    }
  }, [apiError]);


  const handleAnswer = (value: boolean) => {
    const newAnswers = [...answers, value];
    setAnswers(newAnswers);

    const nextIndex = index + 1;
    if (nextIndex >= questions.length) {
      finish(newAnswers);
      return;
    }

    if (nextIndex === 5) {
      if (newAnswers.filter((a) => a).length < 3) {
        finish(newAnswers);
        return;
      }
    }

    setIndex(nextIndex);
  };

  const startTest = () => {
    setShowIntro(false);
    setIndex(0);
  };

  const finish = async (finalAnswers: boolean[]) => {
    setLoading(true);
    setApiError(null);
    
    try {
      const count = finalAnswers.filter((a) => a).length;
      const probability = Math.round((count / questions.length) * 100);
      const summary =
        count >= 5
          ? "In base alle tue risposte potresti avere la scabbia. Contatta un medico per una diagnosi certa."
          : "Dalle tue risposte sembra improbabile che tu abbia la scabbia.";

      const details =
        count >= 5
          ? `Hai risposto \"Sì\" a ${count} domande su ${questions.length}, in particolare ad alcune tipiche della scabbia.`
          : `Solo ${count} risposte su ${questions.length} indicano sintomi compatibili.`;

      // Prepare the input in the format expected by scabiesOraclePrompt
      const symptoms = {
        itching_intensity: finalAnswers[0] ? "severe" : "none",
        itching_worse_at_night: finalAnswers[0] || false,
        rash_present: finalAnswers[1] || false,
        rash_locations: finalAnswers[5] 
          ? ["between_fingers", "wrist_folds", "armpits", "penis"]
          : finalAnswers[6] 
            ? ["between_fingers"] 
            : [],
        visible_burrows: finalAnswers[3] || false,
        onset_days: 30,
        close_contact_scabies: finalAnswers[2] || false,
        lives_in_crowded_setting: finalAnswers[5] || false,
        attempted_treatment: finalAnswers[9] ? "other" : "none",
        skin_scraping_positive: false,
        immune_status: "normal",
        thick_crusts_on_skin: finalAnswers[8] || false,
      };

      const input = {
        messages: [
          {
            role: 'system' as const,
            content: scabiesOraclePrompt
          },
          {
            role: 'user' as const,
            content: JSON.stringify(symptoms)
          }
        ],
        temperature: 0.3,  // Lower temperature for more consistent, structured output
        max_tokens: 1000
      };

      // Call the AI API with rate limiting and error handling
      await callAPI(input);

      // Store the input data for use after the payment
      const sessionData = {
        input: JSON.parse(input.messages[1].content), // Store the parsed symptoms as input
        probability,
        summary,
        details,
        timestamp: new Date().toISOString(),
      };

      localStorage.setItem("scabbiaCheckupData", JSON.stringify(sessionData));
      setShowPay(true);
    } catch (error) {
      console.error("Error in finish function:", error);
      setApiError("Si è verificato un errore durante l'elaborazione. Riprova più tardi.");
    } finally {
      setLoading(false);
    }
  };

  const startCheckout = async () => {
    try {
      setLoading(true);
      const res = await fetch("/api/create-checkout-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          // Include any necessary data for the checkout session
          return_url: `${window.location.origin}/scabbia-checker/success`,
        }),
      });

      if (!res.ok) {
        const errorText = await res.text();
        console.error("Checkout session error", errorText);
        alert("Si è verificato un errore durante l'elaborazione del pagamento. Riprova più tardi.");
        setLoading(false);
        return;
      }

      const data = await res.json();
      if (data.url) {
        // Store a flag in localStorage to indicate payment is in progress
        localStorage.setItem("paymentInProgress", "true");
        window.location.href = data.url;
      } else {
        throw new Error("URL di checkout non ricevuto");
      }
    } catch (err) {
      console.error("Failed to start checkout", err);
      alert("Impossibile avviare il processo di pagamento. Riprova più tardi.");
      setLoading(false);
    }
  };

  // Intro Screen
  if (showIntro) {
    return (
      <>
        <Navbar />
        <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className="max-w-2xl mx-auto text-center space-y-8"
          >
          {/* Hero Icon */}
          <motion.div
            initial={{ y: -20 }}
            animate={{ y: 0 }}
            transition={{ delay: 0.2, type: "spring" }}
            className="flex justify-center"
          >
            <div className="relative">
              <Stethoscope className="w-20 h-20 text-primary" />
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="absolute -top-2 -right-2"
              >
                <Sparkles className="w-8 h-8 text-warning" />
              </motion.div>
            </div>
          </motion.div>

          {/* Title */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-foreground">
              Test AI per la <span className="text-gradient">Scabbia</span>
            </h1>
            <p className="text-xl text-muted-foreground leading-relaxed">
              Analisi avanzata dei sintomi a soli €1 - Risultati comparabili a visita dermatologica
            </p>
            <div className="bg-gradient-to-r from-success/10 to-primary/10 rounded-lg p-4 border border-success/20">
              <div className="flex items-center justify-center space-x-2 text-sm">
                <span className="font-semibold text-success">€1 vs €100+ dermatologo</span>
                <span className="text-muted-foreground">•</span>
                <span className="text-muted-foreground">Accuratezza simile</span>
                <span className="text-muted-foreground">•</span>
                <span className="text-xs text-muted-foreground">Non sostituisce diagnosi medica</span>
              </div>
            </div>
          </motion.div>

          {/* Features */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="grid md:grid-cols-3 gap-6 my-8"
          >
            <div className="flex flex-col items-center space-y-2">
              <Brain className="w-8 h-8 text-primary" />
              <span className="text-sm font-medium">AI Avanzata</span>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <Sparkles className="w-8 h-8 text-warning" />
              <span className="text-sm font-medium">Risultati Istantanei</span>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <Stethoscope className="w-8 h-8 text-success" />
              <span className="text-sm font-medium">Medicamente Accurato</span>
            </div>
          </motion.div>

          {/* CTA Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={startTest}
              className="bg-gradient-to-r from-primary to-primary-dark text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Inizia Test AI - €1
            </motion.button>
          </motion.div>

          {/* Disclaimer */}
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
            className="text-sm text-muted-foreground"
          >
            ⚠️ Questo test non sostituisce una consulenza medica professionale
          </motion.p>
        </motion.div>
        </div>
        <Footer />
      </>
    );
  }

  // Loading Screen
  if (loading) {
    return (
      <>
        <Navbar />
        <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center space-y-6"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Brain className="w-16 h-16 text-primary mx-auto" />
            </motion.div>
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-foreground">Analisi in corso...</h2>
              <p className="text-muted-foreground">La nostra AI sta elaborando le tue risposte</p>
            </div>
            <div className="w-64 mx-auto">
              <Progress value={75} className="h-3" />
            </div>
          </motion.div>
        </div>
        <Footer />
      </>
    );
  }

  // Payment Screen
  if (showPay) {
    return (
      <>
        <Navbar />
        <div className="min-h-screen bg-gradient-to-br from-warning/5 via-background to-warning/10 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="max-w-md mx-auto text-center space-y-6 p-8 bg-card rounded-xl shadow-lg"
          >
            <div className="text-warning">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <div className="space-y-4">
              <h2 className="text-2xl font-bold text-foreground">Risultati Pronti</h2>
              <p className="text-muted-foreground">
                Completa il pagamento di <span className="font-bold text-warning">€1</span> per accedere all&apos;analisi dettagliata
              </p>
              <div className="bg-gradient-to-r from-success/10 to-warning/10 rounded-lg p-3 border border-success/20">
                <div className="text-xs text-center space-y-1">
                  <p className="font-semibold text-success">💡 Investimento intelligente</p>
                  <p className="text-muted-foreground">€1 per analisi AI vs €100+ visita dermatologica</p>
                  <p className="text-xs text-muted-foreground">Risultati comparabili ma non sostitutivi della diagnosi medica</p>
                </div>
              </div>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={startCheckout}
              disabled={loading}
              className="w-full bg-gradient-to-r from-primary to-primary-dark text-white py-3 px-6 rounded-lg font-medium disabled:opacity-50"
            >
              {loading ? "Elaborazione..." : "Paga con Stripe"}
            </motion.button>
            <Link
              href="/"
              className="block text-muted-foreground hover:text-foreground text-sm"
            >
              Torna alla Home
            </Link>
          </motion.div>
        </div>
        <Footer />
      </>
    );
  }

  // Main Test Interface
  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5">
        {/* Header with Progress */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="sticky top-0 z-10 bg-background/80 backdrop-blur-sm border-b p-4"
        >
        <div className="max-w-4xl mx-auto space-y-4">
          <div className="flex items-center justify-between">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowIntro(true)}
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Indietro
            </motion.button>
            <div className="text-sm font-medium text-muted-foreground">
              {index + 1} / {questions.length}
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Progresso</span>
              <span className="font-medium">{Math.round(((index + 1) / questions.length) * 100)}%</span>
            </div>
            <Progress value={((index + 1) / questions.length) * 100} className="h-2" />
          </div>
        </div>
      </motion.div>

      {/* Question Content */}
      <div className="flex-1 flex items-center justify-center p-4 pt-8">
        <div className="w-full max-w-4xl">
          <AnimatePresence mode="wait">
            <motion.div
              key={index}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.4 }}
            >
              <QuestionCard
                question={questions[index].text}
                questionNumber={index + 1}
                totalQuestions={questions.length}
                onAnswer={handleAnswer}
                isLoading={loading}
              />

              {/* Question Description */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="mt-6 text-center"
              >
                <div className="inline-flex items-center gap-3 bg-muted/50 px-4 py-3 rounded-lg">
                  <span className="text-2xl">{questions[index].icon}</span>
                  <p className="text-sm text-muted-foreground max-w-md">
                    {questions[index].description}
                  </p>
                </div>
              </motion.div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
      </div>
      <Footer />
    </>
  );
}
